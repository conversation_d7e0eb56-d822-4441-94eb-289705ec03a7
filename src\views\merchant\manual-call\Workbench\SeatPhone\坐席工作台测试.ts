onMounted(async () => {
  // 更新表单设置
  const taskStore = useTaskStore()
  await taskStore.getEnableFormSetting(true)

  // 更新坐席设置
  seatPhoneStore.seatSetting.autoCallNext = true

  // 测试通话
  // await testCall()
  // 测试客户状态记录
  // testClientStatusList()
})

/**
 * 测试客户状态记录
 */
const testClientStatusList = () => {
  seatPhoneStore.eventList = []
  for (let i = 0; i < 4; i++) {
    const now = dayjs().format('YYYY-MM-DD HH:mm:ss')
    const item = {
      occurrenceTime: now,
      stageString: '测试' + ' ' + now,
      product: 'default_product',
    }
    seatPhoneStore.handleReceiveEvent(item)
  }
  setInterval(() => {
    if (seatPhoneStore.eventList.length === 9) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_key|保通泰康泰超能长青版，基础款0.6元起'
      })
    } else if (seatPhoneStore.eventList.length === 12) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_key2|产品2 产品2 产品2 产品2 产品2 产品2 产品2 产品2 产品2'
      })
    } else if (seatPhoneStore.eventList.length === 15) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_key3 | 分隔符左右有空格'
      })
    } else if (seatPhoneStore.eventList.length === 18) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_unknown|产品名未知'
      })
    } else if (seatPhoneStore.eventList.length === 21) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_123|产品名（带括号）'
      })
    } else if (seatPhoneStore.eventList.length === 24) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_124|产品名不带括号'
      })
    } else if (seatPhoneStore.eventList.length === 27) {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        stageString: '点击立即投保按钮',
        product: 'bt_product_124|产品名不带括号'
      })
    } else {
      seatPhoneStore.handleReceiveEvent({
        occurrenceTime: dayjs(Date.now() + Math.ceil((Math.random() - .5) * 30000)).format('YYYY-MM-DD HH:mm:ss'),
        stageString: '测试测试',
        product: '测试测试',
      })
    }
  }, 1000)
}
/**
 * 测试通话
 */
const testCall = async () => {
  console.log('测试通话')
  havePermission.value = true
  seatPhoneStore.seatOnline = true
  seatPhoneStore.seatTaskList.length = 2

  // @ts-ignore
  seatPhoneStore.clueList = clueListTestData
  // @ts-ignore
  const clue: ClueItem = clueListTestData.at(-1)
  // clue.id = 197
  clue.name = '张三'
  // clue.comment = '备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注'
  clue.comment = '【456】短信内容'
  seatPhoneStore.updateCurrentClue(clue)

  // seatPhoneStore.dialogOfflineVisible = true
  // seatPhoneStore.dialogCallingVisible = true
  // seatPhoneStore.showDialogCallFail('错误原因')
  // seatPhoneStore.dialogExitMonitorVisible = true

  await new Promise((resolve) => {
    setTimeout(resolve, 1000)
  })

  seatPhoneStore.seatPage = SeatPageEnum.PHONE

  // seatPhoneStore.callInfo.callId = ''

  seatPhoneStore.updateCallType({ type: SeatCallTypeEnum.DIRECT })
  seatPhoneStore.seatStatus = SeatStatusEnum.MANUAL_DIRECT_DIALING
  // seatPhoneStore.seatStatus = SeatStatusEnum.MANUAL_DIRECT_POSTING

  // seatPhoneStore.updateCallType({ type: SeatCallTypeEnum.MONITOR })
  // seatPhoneStore.updateCallType({ type: SeatCallTypeEnum.ANSWER })
  // seatPhoneStore.seatStatus = SeatStatusEnum.HUMAN_MACHINE_WINDOW
  // seatPhoneStore.seatStatus = SeatStatusEnum.HUMAN_MACHINE_LISTEN
  // seatPhoneStore.seatStatus = SeatStatusEnum.HUMAN_MACHINE_DIALING
  // seatPhoneStore.seatStatus = SeatStatusEnum.HUMAN_MACHINE_POSTING
  // seatPhoneStore.callRecordForManualDirect = { recordId: '123' }

  seatPhoneStore.openReceptionTimer()
  // seatPhoneStore.receptionSecond = 3600 - 1
  // seatPhoneStore.showIncomingCall()
  // seatPhoneStore.notifyTaskStart([{ taskName: '123' }])
  // seatPhoneStore.notifyTaskStart([{ taskName: '123' }, { taskName: '456' }])
  // seatPhoneStore.notifyCheckOut('123123')

  // seatPhoneStore.startHeartbeatTimer()

  // await seatPhoneStore.getCallCarryInfo()
  // seatPhoneStore.updateCallCarryInfo({
  //   name: '张三',
  //   age: 17,
  // })
  // seatPhoneStore.updateCallCarryInfo({
  //   name: null,
  //   age: null,
  // })
  setTimeout(() => {
    seatPhoneStore.updateCallCarryInfo({
      name: '张三',
      age: 17,
    })
  }, 3000)
}
// 测试通知
// ElNotification({
//   title: '签入任务已全部停止',
//   message: '请签入其他任务或联系任务管理员开启新任务',
//   type: 'warning',
//   duration: 5000,
//   offset: 40,
//   customClass: 'auto-check-out-task-notification',
//   appendTo: document.body,
// })
