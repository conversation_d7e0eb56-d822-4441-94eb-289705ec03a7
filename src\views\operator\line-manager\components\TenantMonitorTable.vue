<template>
  <div class="tw-flex tw-flex-col tw-overflow-hidden tw-bg-[#fff] tw-flex-grow tw-flex-shrink">
    <div v-if="props.isDetail"class="tw-mb-[8px] tw-px-[12px] tw-flex tw-justify-between tw-items-center tw-text-[13px]">
      <div class="tw-flex tw-items-center">
        <span class="tw-ml-[6px] tw-text-[var(--primary-black-color-300)]">已选：</span>
        <span class="tw-text-[var(--primary-black-color-300)]">{{ selectData?.length || 0 }}</span>
        <span class="tw-text-[var(--primary-black-color-300)]">/</span>
        <span class="tw-text-[var(--primary-black-color-300)]">{{ tableData?.length || 0 }}</span>
      </div>
      <div>
        <slot name="btn" :rows="selectData"></slot>
      </div>
    </div>
    
    <el-table
      :data="tableTempData"
      ref="tableRef"
      v-loading="props.loading"
      class="tw-grow"
      row-key="tenantLineNumber"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      stripe
      border
    >
      <el-table-column v-if="props.isDetail" width="36" align="left" type="selection"></el-table-column>
      <el-table-column v-if="selectCols.includes('商户线路名称')" property="tenantLineName" :label="userInfo.accountType === 0 ? '商户线路名称' : '线路名称'" align="left" min-width="360" :formatter="formatterEmptyData" show-overflow-tooltip fixed="left">
        <template #default="{ row }">
          <div @click="copyText(row.tenantLineName)" class="tw-truncate tw-cursor-pointer" :class="row.tenantLineNumber===props.lineNumber ? 'tw-text-[#165DFF]':''">
            {{ row.tenantLineName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('线路类型')" property="tenantLineType" label="线路类型" align="center" width="80" :formatter="formatterEmptyData" show-overflow-tooltip fixed="left">
        <template #default="{ row }">
          {{ getMerchantLineTypeText(row?.tenantLineType) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('线路编号')" property="tenantLineNumber" label="线路编号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="props.isDetail && selectCols.includes('生效状态')" property="status" label="生效状态" align="center" min-width="100" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <span class="status-box" :class="row.status === 'ENABLE' ? 'green-status' : 'orange-status'">
            {{ row.status === 'ENABLE' ? '启用' : '停用' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('并发')" property="concurrency" label="并发(实时/支配/上限)" sortable="custom" align="left" min-width="200" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('呼叫数')" property="currentlyCallNum" sortable="custom" label="呼叫数" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('接通数')" property="currentlyConnectedNum" sortable="custom" label="接通数" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span>
            {{ row.currentlyConnectedNum ?? '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('接通率')" property="currentlyConnectedRate" sortable="custom" label="接通率" align="left" min-width="80" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :style="getMonitorColor(row.currentlyConnectedRate, 'currentlyConnectedRate')">
            {{ row.currentlyConnectedRate ?? '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('平均等待时长')" property="currentlyWaitSecond" label="平均等待时长" align="left" min-width="120">
        <template #default="{ row }">
          <span>
            {{ row.currentlyWaitSecond && row.currentlyCallNum ? formatDuration(row.currentlyWaitSecond/row.currentlyCallNum) : '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('平均通话时长')" property="currentlyCallDurationSecond" label="平均通话时长" align="left" min-width="120">
        <template #default="{ row }">
          <span>
            {{ row.currentlyCallDurationSecond && row.currentlyConnectedNum ? formatDuration(row.currentlyCallDurationSecond/row.currentlyConnectedNum) : '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('无声通话占比')" property="currentlySilenceCall" sortable="custom" label="无声通话占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :style="getMonitorColor(row.currentlySilenceCall, 'currentlySilenceCall')">
            {{ row.currentlySilenceCall ?? '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('送呼失败')" property="currentlyCallFailed" sortable="custom" label="送呼失败" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :style="getMonitorColor(row.currentlyCallFailed, 'currentlyCallFailed')">
            {{ row.currentlyCallFailed ?? '-' }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column v-if="selectCols.includes('路由失败')" property="currentlyRoutingFail" sortable="custom" label="路由失败" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('沉默挂机')" property="currentlySilenceHangup" sortable="custom" label="沉默挂机" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :style="getMonitorColor(row.currentlySilenceHangup, 'currentlySilenceHangup')">
            {{ row.currentlySilenceHangup ?? '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('小助理')" property="currentlyAssistant" sortable="custom" label="小助理" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('运营商提示音')" property="currentlyPromptSound" sortable="custom" label="运营商提示音" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :style="getMonitorColor(row.currentlyPromptSound, 'currentlyPromptSound')">
            {{ row.currentlyPromptSound ?? '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('秒挂（1s）')" property="currentlyOneSecondConnected" sortable="custom" label="秒挂（1s）" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('秒挂（2s）')" property="currentlyTwoSecondConnected" sortable="custom" label="秒挂（2s）" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('转人工占比')" property="currentlyTransCallSeatNum" sortable="custom" label="转人工占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('A类占比')" property="currentlyClassANum" sortable="custom" label="A类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('B类占比')" property="currentlyClassBNum" sortable="custom" label="B类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('C类占比')" property="currentlyClassCNum" sortable="custom" label="C类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('D类占比')" property="currentlyClassDNum" sortable="custom" label="D类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!props.isDetail && selectCols.includes('优先状态')" property="isPriority" label="优先状态" align="left" width="80" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('商户名称')" property="tenantName" label="商户名称" align="left" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('商户编号')" property="tenantNumber" label="商户编号" align="left" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('所属账号')" property="account" label="所属账号" align="left" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <!-- <el-table-column v-if="props.isDetail" align="left" prop="concurrentLimit" label="最大可支配并发" width="120">
        <template #default="{ row }">
          <div class="tw-flex tw-items-center tw-flex-nowrap">
            <span>
              {{ row.concurrentLimit ?? '-' }}
            </span>
            <div class="tw-ml-[8px]">
              <el-button link @click="onClickEditMaxConcurrency(row)">
                <el-icon :size="18" color="#165DFF">
                  <SvgIcon name="edit2" />
                </el-icon>
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column v-if="props.isDetail" property="isPriority" label="优先状态" align="center" fixed="right" width="80">
        <template #default="{ row, $index }">
          <slot name="prior" :row="row" :index="$index"></slot>
        </template>
      </el-table-column>
      <el-table-column v-if="props.isDetail" property="isTempStop" label="临停状态" align="center" fixed="right" width="80">
        <template #default="{ row, $index }">
          <slot name="hold" :row="row" :index="$index"></slot>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="props.isDetail ? 280 : 100" align="right" fixed="right">
        <template #default="{ row, $index }">
          <slot name="operate" :row="row" :index="$index"></slot>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="tableData?.length || 0"
      @search="search()"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { computed, watch, ref, onDeactivated, onUnmounted, onMounted } from 'vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { handleTableSort, copyText, formatterEmptyData, formatDuration } from '@/utils/utils'
import { useUserStore } from '@/store/user'
import { tableHeaderStyle } from '@/assets/js/constant'
import { getMerchantLineTypeText } from '@/utils/line'
import { TenantMonitorInfoItem } from '@/type/line'
import { getMonitorColor } from './constant'

const globalInfo = useGlobalStore()
const userInfo = useUserStore()
const selectCols = computed(() => userInfo.colInfo[
  userInfo.accountType === 0 ? 'tenant-monitor' : 'merchant-tenant-line-monitor'
] || [])

// props和emits
const emits = defineEmits(['show-block', 'update:table',])
const props = withDefaults(defineProps<{
  tableData: TenantMonitorInfoItem[]
  isDetail?: boolean, // 是否展示生效状态、临停、并发，批量选择和操作等
  loading: boolean,
  showPagination?: boolean
  recentMin?: number | null,
  lineNumber?: string,
}>(), {
  showPagination: false,
  isDetail: false
})

// 分页
const currentPage = ref(1)
const pageSize = ref(globalInfo.pageSize || 20)
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  globalInfo.pageSize = s
}
const search = () => {
  emits('update:table')
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}

const selectData = ref<TenantMonitorInfoItem[] | null>(null)
const tableRef = ref()
const handleSelectionChange = (val: TenantMonitorInfoItem[]) => {
  selectData.value = val || []
}

const init = async () => {
  pageSize.value = globalInfo.pageSize || 20
}
onMounted(() => {
  init()
})
// 任务数据列表
const tableData = ref<TenantMonitorInfoItem[] | null>(props.tableData || [])
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return props.showPagination ?
  data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
  : data
})

watch([() => props.recentMin, () => props.loading], () => {
  tableData.value = props.tableData || []
  total.value = tableData.value.length || 0
  tableRef.value?.clearSelection()
}, {immediate: true})

onDeactivated(() => {
  tableData.value = null
})
onUnmounted(() => {
  tableData.value = null
})
</script>
<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 8px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
</style>
